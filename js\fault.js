// 工具函数
const utils = {
    formatDateTime(date) {
        return new Date(date).toLocaleString('zh-CN', {
            year: '2-digit',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    safeValue(value, defaultValue = '') {
        return value || defaultValue;
    },

    calculateTimeDifference(startDate, endDate, defaultValue = '') {
        if (!startDate || !endDate) return defaultValue;
        
        try {
            // 确保日期格式正确
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            // 验证日期有效性
            if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                return defaultValue;
            }
            
            // 计算时间差（毫秒）
            const diffInMs = end.getTime() - start.getTime();
            
            // 转换为分钟并四舍五入
            const diffInMinutes = Math.round(diffInMs / (1000 * 60));
            
            // 确保非负数
            return diffInMinutes >= 0 ? diffInMinutes : defaultValue;
        } catch (e) {
            console.error('计算时间差出错:', e);
            return defaultValue;
        }
    },

    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
        const d = new Date(date);
        let month = '' + (d.getMonth() + 1);
        let day = '' + d.getDate();
        const year = d.getFullYear();

        if (month.length < 2) month = '0' + month;
        if (day.length < 2) day = '0' + day;

        return [year, month, day].join('-');
    },

    // 获取最近N天的日期数组
    getLastNDays(n) {
        const dates = [];
        const today = new Date();
        for (let i = n - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(today.getDate() - i);
            dates.push(this.formatDate(date));
        }
        return dates;
    },

    // 获取最近N周的周一日期数组
    getLastNWeeks(n) {
        const weeks = [];
        const today = new Date();
        for (let i = n - 1; i >= 0; i--) {
            const monday = new Date(today);
            monday.setDate(today.getDate() - today.getDay() + 1 - i * 7);
            weeks.push(this.formatDate(monday));
        }
        return weeks;
    },

    // 获取最近N个月的第一天日期数组
    getLastNMonths(n) {
        const months = [];
        const today = new Date();
        for (let i = n - 1; i >= 0; i--) {
            const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
            months.push(this.formatDate(month));
        }
        return months;
    }
};

// 故障管理类
class FaultManager {
    constructor() {
        this.initializeEventListeners();
        this.modal = null;
        this.faultData = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 0;
        this.totalRecords = 0;
        this.modalSelectedFiles = new Map(); // 初始化模态框文件选择
        // 获取用户信息
        this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
        // 初始化图表实例
        this.charts = {};
    }

    // 初始化所有事件监听器
    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            // 检查用户是否已登录
            if (!this.userInfo) {
                window.location.href = 'login.html';
                return;
            }

            this.initFaultRegisterForm();
            this.loadFaultList();
            this.initModal();
            this.initSearchForm();
            this.initFileUpload();
            this.initPagination();
            this.loadAllOptions();
            this.initOptionChaining();
            this.initRegisterOptionChaining();
            // 初始化图表
            this.initCharts();
        });
    }

    // 初始化图表
    initCharts() {
        // 确保页面中有图表容器
        if (document.getElementById('chart-monthly')) {
            this.charts.monthly = echarts.init(document.getElementById('chart-monthly'));
            this.charts.weekly = echarts.init(document.getElementById('chart-weekly'));
            this.charts.daily = echarts.init(document.getElementById('chart-daily'));
            this.charts.unitLoss = echarts.init(document.getElementById('chart-unit-loss'));
            this.charts.sectionFault = echarts.init(document.getElementById('chart-section-fault'));
            this.charts.lineFault = echarts.init(document.getElementById('chart-line-fault'));
            
            // 设置初始空图表
            this.setEmptyCharts();
        }
    }

    // 设置空图表
    setEmptyCharts() {
        const option = {
            title: {
                text: '暂无数据',
                left: 'center',
                top: 'center'
            }
        };

        Object.values(this.charts).forEach(chart => {
            chart.setOption(option, true);
        });
    }

    // 更新图表数据
    updateCharts(faults) {
        if (!this.charts.monthly) return;

        // 处理图表数据
        this.updateTimeCharts(faults);
        this.updatePieCharts(faults);
    }

    // 更新时间趋势图表
    updateTimeCharts(faults) {
        // 按月统计
        const monthlyData = this.aggregateDataByPeriod(faults, 'monthly');
        const monthlyOption = this.createTimeChartOption(
            '', 
            monthlyData.dates, 
            monthlyData.lossData, 
            monthlyData.countData,
            '月'
        );
        this.charts.monthly.setOption(monthlyOption, true);

        // 按周统计
        const weeklyData = this.aggregateDataByPeriod(faults, 'weekly');
        const weeklyOption = this.createTimeChartOption(
            '', 
            weeklyData.dates, 
            weeklyData.lossData, 
            weeklyData.countData,
            '周'
        );
        this.charts.weekly.setOption(weeklyOption, true);

        // 按天统计
        const dailyData = this.aggregateDataByPeriod(faults, 'daily');
        const dailyOption = this.createTimeChartOption(
            '', 
            dailyData.dates, 
            dailyData.lossData, 
            dailyData.countData,
            '日'
        );
        this.charts.daily.setOption(dailyOption, true);
    }

    // 按周期聚合数据
    aggregateDataByPeriod(faults, period) {
        let dates = [];
        let dateMap = new Map();

        // 根据周期类型确定日期范围
        switch (period) {
            case 'monthly':
                dates = utils.getLastNMonths(3);
                break;
            case 'weekly':
                dates = utils.getLastNWeeks(4);
                break;
            case 'daily':
                dates = utils.getLastNDays(7);
                break;
        }

        // 初始化日期映射
        dates.forEach(date => {
            dateMap.set(date, { loss: 0, count: 0 });
        });

        // 聚合故障数据
        faults.forEach(fault => {
            if (!fault.datetime || !fault.closetime) return;

            const key = this.getDateKey(fault.datetime, period);
            if (dateMap.has(key)) {
                const lossTime = utils.calculateTimeDifference(fault.datetime, fault.closetime, 0);
                const entry = dateMap.get(key);
                entry.loss += lossTime;
                entry.count += 1;
                dateMap.set(key, entry);
            }
        });

        // 构造返回数据
        const lossData = [];
        const countData = [];

        dates.forEach(date => {
            const entry = dateMap.get(date);
            lossData.push(entry ? entry.loss : 0);
            countData.push(entry ? entry.count : 0);
        });

        return {
            dates: dates,
            lossData: lossData,
            countData: countData
        };
    }

    // 获取日期键值
    getDateKey(dateStr, period) {
        const date = new Date(dateStr);
        switch (period) {
            case 'monthly':
                // 返回月份的第一天
                return utils.formatDate(new Date(date.getFullYear(), date.getMonth(), 1));
            case 'weekly':
                // 返回周一的日期
                const monday = new Date(date);
                monday.setDate(date.getDate() - date.getDay() + 1);
                return utils.formatDate(monday);
            case 'daily':
                // 返回日期本身
                return utils.formatDate(date);
            default:
                return utils.formatDate(date);
        }
    }

    // 创建时间趋势图表选项
    createTimeChartOption(title, dates, lossData, countData, unit) {
        return {
            title: {
                text: title
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['LOSS时间(分钟)', '故障次数'],
                bottom: 0
            },
            xAxis: {
                type: 'category',
                data: dates.map(date => {
                    if (unit === '月') {
                        return date.substring(0, 7); // YYYY-MM
                    } else if (unit === '周') {
                        return date.substring(5); // MM-DD
                    } else {
                        return date.substring(5); // MM-DD
                    }
                })
            },
            yAxis: [
                {
                    type: 'value',
                    name: 'LOSS时间(分钟)',
                    splitLine: {
                        show: true
                    }
                },
                {
                    type: 'value',
                    name: '故障次数',
                    splitLine: {
                        show: false
                    }
                }
            ],
            series: [
                {
                    name: 'LOSS时间(分钟)',
                    type: 'bar',
                    data: lossData
                },
                {
                    name: '故障次数',
                    type: 'line',
                    yAxisIndex: 1,
                    data: countData
                }
            ]
        };
    }

    // 更新饼图
    updatePieCharts(faults) {
        // 按unit统计LOSS时间
        const unitLossData = this.aggregateByUnitLoss(faults);
        const unitLossOption = this.createPieChartOption('各UNIT的LOSS时间占比', unitLossData);
        this.charts.unitLoss.setOption(unitLossOption, true);

        // 按section统计故障次数（修改为按科室统计）
        const sectionFaultData = this.aggregateBySectionCount(faults);
        const sectionFaultOption = this.createPieChartOption('各科室的故障次数占比', sectionFaultData);
        this.charts.sectionFault.setOption(sectionFaultOption, true);

        // 按line统计故障次数
        const lineFaultData = this.aggregateByLineCount(faults);
        const lineFaultOption = this.createPieChartOption('各LINE的故障次数占比', lineFaultData);
        this.charts.lineFault.setOption(lineFaultOption, true);
    }

    // 按unit聚合LOSS时间
    aggregateByUnitLoss(faults) {
        const dataMap = new Map();

        faults.forEach(fault => {
            if (!fault.unit || !fault.datetime || !fault.closetime) return;
            
            const lossTime = utils.calculateTimeDifference(fault.datetime, fault.closetime, 0);
            if (lossTime > 0) {
                if (dataMap.has(fault.unit)) {
                    dataMap.set(fault.unit, dataMap.get(fault.unit) + lossTime);
                } else {
                    dataMap.set(fault.unit, lossTime);
                }
            }
        });

        // 转换为数组并按值排序
        const dataArray = Array.from(dataMap.entries())
            .map(([name, value]) => ({ name, value }))
            .sort((a, b) => b.value - a.value);

        return dataArray;
    }

    // 按section聚合故障次数（替代原来的按unit聚合故障次数）
    aggregateBySectionCount(faults) {
        const dataMap = new Map();

        faults.forEach(fault => {
            if (!fault.section) return;
            
            if (dataMap.has(fault.section)) {
                dataMap.set(fault.section, dataMap.get(fault.section) + 1);
            } else {
                dataMap.set(fault.section, 1);
            }
        });

        // 转换为数组并按值排序
        const dataArray = Array.from(dataMap.entries())
            .map(([name, value]) => ({ name, value }))
            .sort((a, b) => b.value - a.value);

        return dataArray;
    }

    // 按line聚合故障次数
    aggregateByLineCount(faults) {
        const dataMap = new Map();

        faults.forEach(fault => {
            if (!fault.line) return;
            
            if (dataMap.has(fault.line)) {
                dataMap.set(fault.line, dataMap.get(fault.line) + 1);
            } else {
                dataMap.set(fault.line, 1);
            }
        });

        // 转换为数组并按值排序
        const dataArray = Array.from(dataMap.entries())
            .map(([name, value]) => ({ name, value }))
            .sort((a, b) => b.value - a.value);

        return dataArray;
    }

    // 创建饼图选项
    createPieChartOption(title, data) {
        // 只显示前五项数据的标签
        const topFiveData = data.map((item, index) => {
            if (index < 5) {
                return item;
            } else {
                return {
                    name: item.name,
                    value: item.value,
                    label: {
                        show: false
                    }
                };
            }
        });
        
        return {
            title: {
                text: title
            },
            tooltip: {
                trigger: 'item'
            },
            series: [
                {
                    type: 'pie',
                    radius: '70%',
                    data: topFiveData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
    }

    // 初始化搜索表单
    initSearchForm() {
        const searchForm = document.getElementById('searchForm');
        if (!searchForm) {
            console.error('搜索表单未找到');
            return;
        }

        // 获取日期输入框引用（仅用于重置功能）
        const startDateInput = searchForm.querySelector('#search-start-date');
        const endDateInput = searchForm.querySelector('#search-end-date');

        // 处理搜索表单提交
        searchForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(searchForm);
            const searchParams = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value) {
                    searchParams.append(key, value);
                }
            }

            const queryString = searchParams.toString();
            await this.loadFaultList(queryString);
        });

        // 处理重置按钮点击
        const resetBtn = searchForm.querySelector('.reset-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                
                // 重置所有选择框为第一个选项（ALL）
                searchForm.querySelectorAll('select').forEach(select => {
                    select.selectedIndex = 0;
                });

                // 清空关键词输入框
                const keywordInput = searchForm.querySelector('#search-keyword');
                if (keywordInput) {
                    keywordInput.value = '';
                }

                // 清空日期输入框
                if (startDateInput) startDateInput.value = '';
                if (endDateInput) endDateInput.value = '';

                // 重新加载所有选项
                await this.loadAllOptions();

                // 重新加载故障列表数据
                await this.loadFaultList();
            });
        }
    }

    // 初始化故障上传表单
    initFaultRegisterForm() {
        const form = document.querySelector('.register-form');
        if (!form) return;

        // 自动填充科室
        const sectionSelect = form.querySelector('#register-section');
        if (sectionSelect && this.userInfo?.section) {
            // 添加用户科室选项
            const option = document.createElement('option');
            option.value = this.userInfo.section;
            option.textContent = this.userInfo.section;
            sectionSelect.innerHTML = ''; // 清空现有选项
            sectionSelect.appendChild(option);
            sectionSelect.value = this.userInfo.section;
            sectionSelect.disabled = true; // 禁用选择框

            // 立即触发加载相关选项
            this.loadOptions('line', { section: this.userInfo.section }, 'register-line');
            this.loadOptions('unit', { section: this.userInfo.section }, 'register-unit');
        }

        // 自动填充记录人
        const recorderInput = form.querySelector('#register-recorder');
        if (recorderInput && this.userInfo?.name) {
            recorderInput.value = this.userInfo.name;
            recorderInput.readOnly = true;
        }
    }

    // 加载故障列表
    async loadFaultList(queryString = '') {
        try {
            // 添加分页参数
            const params = new URLSearchParams(queryString);
            params.set('page', this.currentPage);
            params.set('limit', this.pageSize);

            // 如果没有指定科室参数，默认使用当前用户的科室
            if (!params.has('section') && this.userInfo?.section) {
                params.set('section', this.userInfo.section);
                
                // 同时更新搜索表单中的科室选择
                const sectionSelect = document.getElementById('search-section');
                if (sectionSelect) {
                    sectionSelect.value = this.userInfo.section;
                    // 触发 change 事件以更新相关的 line 和 unit 选项
                    sectionSelect.dispatchEvent(new Event('change'));
                }
            }

            const url = `php/get_faults.php?${params.toString()}`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || '查询失败');
            }

            this.faultData = result.data;
            this.updateFaultTable();
            this.updatePagination(result.pagination);
            
            // 获取所有数据用于图表显示
            await this.loadAllFaultDataForCharts(queryString);

        } catch (error) {
            console.error('加载故障列表失败：', error);
            alert('加载失败：' + error.message);
        }
    }

    // 获取所有故障数据用于图表显示
    async loadAllFaultDataForCharts(queryString = '') {
        try {
            // 移除分页参数，获取所有数据
            const params = new URLSearchParams(queryString);
            
            
            params.set('page', 1);
            params.set('limit', 999);
            
            // 如果没有指定科室参数，默认使用当前用户的科室
            if (!params.has('section') && this.userInfo?.section) {
                params.set('section', this.userInfo.section);
            }

            const url = `php/get_faults.php?${params.toString()}`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || '查询失败');
            }

            // 更新图表
            this.updateCharts(result.data);

        } catch (error) {
            console.error('加载全部故障数据失败：', error);
            // 不显示错误提示，因为主列表已经加载成功
        }
    }

    // 更新故障表格
    updateFaultTable() {
        const tbody = document.querySelector('#searchTab .data-table tbody');
        if (!tbody) {
            console.error('未找到表格体元素');
            return;
        }

        if (!this.faultData || this.faultData.length === 0) {
            tbody.innerHTML = '<tr><td colspan="13" style="text-align: center;">未找到匹配的记录</td></tr>';
            return;
        }

        tbody.innerHTML = this.faultData.map(fault => this.generateFaultRow(fault)).join('');
        this.bindDetailButtons();
        
        console.log(`更新表格：显示 ${this.faultData.length} 条记录`); // 调试用
    }

    // 生成故障行HTML
    generateFaultRow(fault) {
        const datetime = utils.formatDateTime(fault.datetime);
        // 检查是否显示删除按钮（仅管理员可见）
        const isAdmin = this.userInfo?.allevel === 1;
        const deleteButton = isAdmin ?
            `<button class="fault-action-btn fault-action-btn-danger" data-fault-id="${fault.id}">删除</button>` :
            '';

        return `
            <tr>
                <td>${datetime.split(' ')[0]}</td>
                <td><strong>${utils.calculateTimeDifference(fault.datetime, fault.closetime, '')}min</strong></td>
                <td>${utils.safeValue(fault.issue)}</td>
                <td>${utils.safeValue(fault.phenomenon)}</td>
                <td>${utils.safeValue(fault.measures)}</td>
                <td>${utils.safeValue(fault.section)}</td>
                <td>${utils.safeValue(fault.line)}</td>
                <td>${utils.safeValue(fault.unit)}</td>
                <td>${utils.safeValue(fault.category)}</td>
                <td>${utils.safeValue(fault.direction)}</td>
                <td>${utils.safeValue(fault.status)}</td>
                <td>${utils.safeValue(fault.responsible)}</td>
                <td>
                    <div class="fault-action-buttons">
                        <button class="btn-view fault-action-btn" data-fault-id="${fault.id}">详情</button>
                        ${deleteButton}
                    </div>
                </td>
            </tr>
        `;
    }

    // 绑定详情按钮事件
    bindDetailButtons() {
        // 绑定详情按钮
        const viewButtons = document.querySelectorAll('#searchTab .btn-view');
        viewButtons.forEach(button => {
            button.addEventListener('click', () => {
                const faultId = button.getAttribute('data-fault-id');
                this.showFaultDetail(faultId);
            });
        });

        // 绑定删除按钮（仅管理员可见）
        const deleteButtons = document.querySelectorAll('#searchTab .fault-action-btn-danger');
        deleteButtons.forEach(button => {
            button.addEventListener('click', () => {
                const faultId = button.getAttribute('data-fault-id');
                this.deleteFaultRecord(faultId);
            });
        });
    }

    // 删除故障记录
    async deleteFaultRecord(faultId) {
        // 二次确认
        if (!confirm('是否确认删除该条故障记录？')) {
            return;
        }

        try {
            const response = await fetch('php/delete_fault.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: faultId })
            });

            const result = await response.json();
            if (result.success) {
                alert('删除成功');
                // 重新加载故障列表
                await this.loadFaultList();
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('删除失败:', error);
            alert('删除失败：' + error.message);
        }
    }

    // 初始化模态框
    initModal() {
        this.modal = document.getElementById('faultDetailModal');
        if (!this.modal) return;

        // 关闭模态框的方法
        const closeModal = () => {
            this.modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        };

        // 绑定关闭按钮事件
        this.modal.querySelector('.close')?.addEventListener('click', closeModal);
        this.modal.querySelector('.btn-return')?.addEventListener('click', closeModal);

        // 点击模态框外部关闭
        this.modal.addEventListener('click', e => {
            if (e.target === this.modal) closeModal();
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', e => {
            if (e.key === 'Escape' && this.modal.style.display === 'block') {
                closeModal();
            }
        });
    }

    // 显示故障详情
    async showFaultDetail(id) {
        if (!this.modal) return;

        // 从已加载的数据中查找故障详情
        const faultData = this.faultData.find(fault => fault.id === parseInt(id));
        if (!faultData) {
            console.error('未找到ID为', id, '的故障数据');
            return;
        }

        // 显示模态框
        this.modal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // 更新模态框内容
        this.updateModalContent(faultData);

        // 处理状态切换权限
        this.handleStatusControl(faultData);

        // 加载并显示附件
        await this.loadFaultFiles(id);
    }

    // 更新模态框内容
    updateModalContent(data) {
        // 更新标题
        this.modal.querySelector('.modal-header h2').textContent = 
            `${data.issue}`;

        // 更新详情表格
        const detailRow = this.modal.querySelector('.detail-table tr:nth-child(2)');
        if (detailRow) {
            detailRow.innerHTML = `
                <td>${utils.safeValue(data.section)}</td>
                <td>${utils.safeValue(data.line)}</td>
                <td>${utils.safeValue(data.unit)}</td>
                <td>${utils.safeValue(data.category)}</td>
                <td>${utils.safeValue(data.keyword)}</td>
                <td>${utils.safeValue(data.direction)}</td>
                <td>${utils.safeValue(data.related_parts)}</td>
                <td>${utils.safeValue(data.model)}</td>
                <td>${utils.formatDateTime(data.datetime)}</td>
                <td>${utils.safeValue(data.status)}</td>
                <td>${utils.safeValue(data.responsible)}</td>
            `;
        }

        // 更新其他详情部分
        const sections = this.modal.querySelectorAll('.detail-section');
        sections.forEach(section => {
            const title = section.querySelector('h3').textContent;
            const content = section.querySelector('.section-content');
            
            switch (title) {
                case '问题现象':
                    content.textContent = data.phenomenon || '';
                    break;
                case '原因分析':
                    content.textContent = data.analysis || '';
                    break;
                case '改善措施':
                    if (data.measures) {
                        const measures = data.measures.split('\n');
                        content.innerHTML = measures.map(m => `<p>${m}</p>`).join('');
                    } else {
                        content.innerHTML = '';
                    }
                    break;
                case '相关图片':
                    // TODO: 处理图片显示逻辑
                    content.innerHTML = '暂无图片';
                    break;
            }
        });
    }

    // 处理状态切换权限
    handleStatusControl(faultData) {
        const changeStatusDiv = this.modal.querySelector('.change-status');
        const modifyButton = this.modal.querySelector('.btn-modify');

        // 检查权限：故障记录者本人或管理员（allevel === 1）
        const hasPermission = faultData.recorder === this.userInfo?.name || this.userInfo?.allevel === 1;

        // 设置状态切换区域的可见性
        changeStatusDiv.style.display = 'none';

        if (hasPermission) {
            // 修改按钮状态
            modifyButton.disabled = false;
            modifyButton.style.opacity = '1';
            modifyButton.textContent = '编辑';
            // 变更onclick为toggleEditMode
            modifyButton.onclick = () => this.toggleEditMode(faultData);
        } else {
            // 修改按钮状态
            modifyButton.disabled = true;
            modifyButton.style.opacity = '0.5';
            modifyButton.onclick = null;
        }
    }

    // 新增：切换编辑模式
    toggleEditMode(faultData) {
        const modifyButton = this.modal.querySelector('.btn-modify');
        const isEditMode = modifyButton.textContent === '提交';
        
        if (isEditMode) {
            // 如果当前是编辑模式，执行提交操作
            this.submitFaultUpdate(faultData.id);
        } else {
            // 切换到编辑模式
            modifyButton.textContent = '提交';
            
            // 显示状态切换区域
            const changeStatusDiv = this.modal.querySelector('.change-status');
            changeStatusDiv.style.display = 'block';
            
            // 设置当前选中的状态
            const radioButtons = changeStatusDiv.querySelectorAll('input[type="radio"]');
            radioButtons.forEach(radio => {
                radio.checked = radio.value === faultData.status;
                radio.disabled = false;
            });
            
            // 将内容区域转换为textarea
            const sections = this.modal.querySelectorAll('.detail-section');
            sections.forEach(section => {
                const title = section.querySelector('h3').textContent;
                const content = section.querySelector('.section-content');
                
                // 只处理需要编辑的区域
                if (title === '问题现象' || title === '原因分析' || title === '改善措施') {
                    const currentText = content.textContent.trim();
                    
                    // 创建textarea并设置内容
                    const textarea = document.createElement('textarea');
                    textarea.rows = 4;
                    textarea.className = 'edit-textarea';
                    textarea.dataset.field = title === '问题现象' ? 'phenomenon' : 
                                           title === '原因分析' ? 'analysis' : 'measures';
                    textarea.value = currentText;
                    
                    // 替换原有内容
                    content.innerHTML = '';
                    content.appendChild(textarea);
                } else if (title === '相关附件') {
                    // 为附件区域添加文件上传功能
                    this.setupFileUploadInModal(content, faultData.id);
                }
            });
        }
    }

    // 在模态框中设置文件上传功能
    setupFileUploadInModal(fileSection, faultId) {
        // 保存原有的附件内容
        const existingFiles = fileSection.innerHTML;

        // 创建文件上传区域
        const uploadContainer = document.createElement('div');
        uploadContainer.className = 'modal-file-upload-container';
        uploadContainer.innerHTML = `
            <div class="existing-files">
                ${existingFiles}
            </div>
            <div class="new-file-upload">
                <h4>添加新附件</h4>
                <input type="file" id="modalFileUpload" multiple accept="image/*,.pdf,.doc,.docx,.txt,.xlsx,.xls">
                <div class="modal-file-list">
                    <!-- 新选择的文件将在这里显示 -->
                </div>
            </div>
        `;

        // 替换原有内容
        fileSection.innerHTML = '';
        fileSection.appendChild(uploadContainer);

        // 初始化文件上传功能
        this.initModalFileUpload(faultId);
    }

    // 初始化模态框中的文件上传功能
    initModalFileUpload(faultId) {
        const fileUpload = document.getElementById('modalFileUpload');
        const fileList = document.querySelector('.modal-file-list');

        if (!fileUpload || !fileList) return;

        // 存储新选择的文件
        this.modalSelectedFiles = new Map();

        fileUpload.addEventListener('change', (e) => {
            const newFiles = Array.from(e.target.files);

            // 添加新选择的文件到已有文件列表中
            newFiles.forEach(file => {
                const fileId = Date.now() + '-' + file.name;
                this.modalSelectedFiles.set(fileId, file);
                this.addFileToModalList(fileId, file);
            });

            // 清空input，允许重复选择相同文件
            fileUpload.value = '';
        });
    }

    // 添加文件到模态框列表
    addFileToModalList(fileId, file) {
        const fileList = document.querySelector('.modal-file-list');
        if (!fileList) return;

        const fileItem = document.createElement('div');
        fileItem.className = 'file-item new-file-item';
        fileItem.innerHTML = `
            <span class="file-name" data-file-id="${fileId}">${file.name}</span>
            <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
            <button type="button" class="btn-delete-file" data-file-id="${fileId}">×</button>
        `;

        // 添加文件名点击事件（预览）
        const fileName = fileItem.querySelector('.file-name');
        fileName.addEventListener('click', () => {
            this.previewModalFile(file);
        });

        // 添加删除按钮事件
        const deleteBtn = fileItem.querySelector('.btn-delete-file');
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.modalSelectedFiles.delete(fileId);
            fileItem.remove();
        });

        fileList.appendChild(fileItem);
    }

    // 预览模态框中的文件
    previewModalFile(file) {
        // 如果是图片，创建预览
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.showImage(e.target.result);
            };
            reader.readAsDataURL(file);
        } else {
            // 对于其他类型的文件，尝试在新窗口中打开
            const fileUrl = URL.createObjectURL(file);
            window.open(fileUrl, '_blank');
        }
    }

    // 提交更新
    async submitFaultUpdate(faultId) {
        const modifyButton = this.modal.querySelector('.btn-modify');

        // 防重复提交：禁用按钮
        if (modifyButton) {
            modifyButton.disabled = true;
            modifyButton.textContent = '提交中...';
        }

        try {
            const selectedStatus = this.modal.querySelector('input[name="status"]:checked')?.value;
            if (!selectedStatus) {
                throw new Error('请选择问题状态');
            }

            // 获取文本区域的值
            const textareas = this.modal.querySelectorAll('.edit-textarea');
            let phenomenon = '';
            let analysis = '';
            let measures = '';

            textareas.forEach(textarea => {
                const field = textarea.dataset.field;
                if (field === 'phenomenon') phenomenon = textarea.value;
                else if (field === 'analysis') analysis = textarea.value;
                else if (field === 'measures') measures = textarea.value;
            });

            // 创建FormData来处理文件上传
            const formData = new FormData();
            formData.append('fault_id', faultId);
            formData.append('status', selectedStatus);
            formData.append('phenomenon', phenomenon);
            formData.append('analysis', analysis);
            formData.append('measures', measures);

            // 添加新上传的文件
            if (this.modalSelectedFiles && this.modalSelectedFiles.size > 0) {
                this.modalSelectedFiles.forEach((file) => {
                    formData.append('files[]', file);
                });
            }

            // 发送请求更新数据
            const response = await fetch('php/update_fault_status.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || '更新失败');
            }

            alert('更新成功');

            // 清理文件选择
            if (this.modalSelectedFiles) {
                this.modalSelectedFiles.clear();
            }

            // 关闭模态框
            this.modal.style.display = 'none';
            document.body.style.overflow = 'auto';

            // 重新加载故障列表
            await this.loadFaultList();

        } catch (error) {
            console.error('更新失败:', error);
            alert('更新失败: ' + error.message);
        } finally {
            // 恢复按钮状态
            if (modifyButton) {
                modifyButton.disabled = false;
                modifyButton.textContent = '提交';
            }
        }
    }

    // 初始化文件上传处理方法
    initFileUpload() {
        const fileUpload = document.getElementById('fileUpload');
        const fileList = document.querySelector('.file-list');
        
        if (!fileUpload || !fileList) return;

        // 存储已选择的文件
        let selectedFiles = new Map();

        fileUpload.addEventListener('change', (e) => {
            const newFiles = Array.from(e.target.files);
            
            // 添加新选择的文件到已有文件列表中
            newFiles.forEach(file => {
                const fileId = Date.now() + '-' + file.name;
                selectedFiles.set(fileId, file);
                addFileToList(fileId, file);
            });

            // 清空input，允许重复选择相同文件
            fileUpload.value = '';
        });

                // 添加文件到列表的函数
                const addFileToList = (fileId, file) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span class="file-name" data-file-id="${fileId}">${file.name}</span>
                        <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                        <button type="button" class="btn-delete-file" data-file-id="${fileId}">×</button>
                    `;
        
                    // 添加文件名点击事件（预览）
                    const fileName = fileItem.querySelector('.file-name');
                    fileName.addEventListener('click', () => {
                        previewFile(file);
                    });
        
                    // 添加删除按钮事件
                    const deleteBtn = fileItem.querySelector('.btn-delete-file');
                    deleteBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        selectedFiles.delete(fileId);
                        fileItem.remove();
                    });
        
                    fileList.appendChild(fileItem);
                };
        
                // 文件预览函数
                const previewFile = (file) => {
                    // 如果是图片，创建预览
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const previewWindow = window.open('', '_blank');
                            previewWindow.document.write(`
                                <img src="${e.target.result}" style="max-width: 100%; height: auto;">
                            `);
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // 对于其他类型的文件，尝试在新窗口中打开
                        const fileUrl = URL.createObjectURL(file);
                        window.open(fileUrl, '_blank');
                    }
                };

        // 表单提交处理
        const form = document.querySelector('.register-form');
        if (form) {
            form.onsubmit = async (e) => {
                e.preventDefault();

                // 验证结束时间必须比发生时间晚至少60分钟
                const startTime = form.querySelector('#register-datetime').value;
                const endTime = form.querySelector('#register-closetime').value;

                if (startTime && endTime) {
                    const diffMinutes = utils.calculateTimeDifference(startTime, endTime, 0);
                    if (diffMinutes < 60) {
                        alert('大故障登录LOSS TIME(结束时间-开始时间)须大于60分钟，瞬间故障请跳转交接登录页面');
                        form.querySelector('#register-closetime').value = '';
                        return;
                    }
                }

                // 防重复提交：显示加载状态
                this.showLoadingModal();

                // 设置超时处理
                const timeoutId = setTimeout(() => {
                    this.showTimeoutWarning();
                }, 30000); // 30秒超时警告

                try {
                    const formData = new FormData(form);

                    // 添加用户科室数据
                    if (this.userInfo?.section) {
                        formData.set('section', this.userInfo.section);
                    }

                    // 添加文件
                    if (selectedFiles.size > 0) {
                        selectedFiles.forEach((file) => {
                            formData.append('files[]', file);
                        });
                    }

                    // 发送请求
                    const response = await fetch('php/submit_fault.php', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    if (result.success) {
                        alert('故障信息已成功添加');
                        form.reset();
                        fileList.innerHTML = '';
                        selectedFiles.clear();
                        this.loadFaultList();

                        // 重新设置科室和记录人
                        this.initFaultRegisterForm();
                    } else {
                        throw new Error(result.message);
                    }
                } catch (error) {
                    console.error('提交错误:', error);
                    alert('提交出错：' + error.message);
                } finally {
                    // 清除超时定时器
                    clearTimeout(timeoutId);
                    // 无论成功还是失败，都要隐藏加载状态
                    this.hideLoadingModal();
                }
            };
        }
    }

    // 添加加载附件的方法
    async loadFaultFiles(faultId) {
        try {
            const response = await fetch(`php/get_fault_files.php?fault_id=${faultId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message);
            }

            const fileSection = this.modal.querySelector('.file-section');
            if (!fileSection) return;

            if (result.data.length === 0) {
                fileSection.innerHTML = '<p>暂无附件</p>';
                return;
            }

            // 显示附件列表，区分图片和非图片文件的处理
            fileSection.innerHTML = result.data.map(file => {
                const isImage = this.isImageFile(file.file_name);
                const fileUrl = `uploads/${file.file_path}`;
                const fileSize = (file.file_size / 1024).toFixed(2);
                
                return `
                    <div class="file-item">
                        <span class="file-name" 
                              data-file-url="${fileUrl}"
                              data-file-name="${file.file_name}"
                              data-is-image="${isImage}"
                              style="cursor: pointer;">
                            ${file.file_name}
                        </span>
                        <span class="file-size">(${fileSize} KB)</span>
                    </div>
                `;
            }).join('');

            // 为所有文件名添加点击事件监听器
            const fileNames = fileSection.querySelectorAll('.file-name');
            fileNames.forEach(fileName => {
                fileName.addEventListener('click', () => {
                    const fileUrl = fileName.dataset.fileUrl;
                    const isImage = fileName.dataset.isImage === 'true';
                    const originalFileName = fileName.dataset.fileName;

                    if (isImage) {
                        this.showImage(fileUrl);
                    } else {
                        this.downloadFile(fileUrl, originalFileName);
                    }
                });
            });

        } catch (error) {
            console.error('加载附件失败:', error);
            const fileSection = this.modal.querySelector('.file-section');
            if (fileSection) {
                fileSection.innerHTML = '<p class="error">加载附件失败</p>';
            }
        }
    }

    // 添加判断文件是否为图片的方法
    isImageFile(fileName) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
        const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        return imageExtensions.includes(ext);
    }

    // 添加显示图片的方法
    showImage(imageUrl) {
        const imageWindow = window.open('', '_blank');
        imageWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>图片预览</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                        background: #f0f0f0;
                    }
                    img {
                        max-width: 100%;
                        max-height: 90vh;
                        object-fit: contain;
                        box-shadow: 0 0 20px rgba(0,0,0,0.15);
                    }
                </style>
            </head>
            <body>
                <img src="${imageUrl}" alt="预览图片">
            </body>
            </html>
        `);
    }

    // 添加下载文件的方法
    downloadFile(fileUrl, fileName) {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 显示加载弹框
    showLoadingModal() {
        const loadingModal = document.getElementById('loadingModal');
        const submitBtn = document.querySelector('.btn-submit');

        if (loadingModal) {
            loadingModal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        if (submitBtn) {
            submitBtn.disabled = true;
        }

        // 阻止页面其他交互
        this.disablePageInteraction();
    }

    // 隐藏加载弹框
    hideLoadingModal() {
        const loadingModal = document.getElementById('loadingModal');
        const loadingText = loadingModal?.querySelector('.loading-text');
        const submitBtn = document.querySelector('.btn-submit');

        if (loadingModal) {
            loadingModal.style.display = 'none';
            loadingModal.classList.remove('timeout');
            document.body.style.overflow = 'auto';
        }

        if (loadingText) {
            loadingText.textContent = '提交中...';
        }

        if (submitBtn) {
            submitBtn.disabled = false;
        }

        // 恢复页面交互
        this.enablePageInteraction();
    }

    // 禁用页面交互
    disablePageInteraction() {
        // 禁用所有表单元素（除了加载弹框内的元素）
        const formElements = document.querySelectorAll('input, select, textarea, button');
        formElements.forEach(element => {
            // 不禁用加载弹框内的元素
            if (!element.closest('#loadingModal')) {
                element.dataset.originalDisabled = element.disabled;
                element.disabled = true;
            }
        });

        // 添加遮罩层样式类
        document.body.classList.add('submitting');
    }

    // 恢复页面交互
    enablePageInteraction() {
        // 恢复所有表单元素的原始状态
        const formElements = document.querySelectorAll('input, select, textarea, button');
        formElements.forEach(element => {
            if (!element.closest('#loadingModal')) {
                const originalDisabled = element.dataset.originalDisabled;
                element.disabled = originalDisabled === 'true';
                delete element.dataset.originalDisabled;
            }
        });

        // 移除遮罩层样式类
        document.body.classList.remove('submitting');
    }

    // 显示超时警告
    showTimeoutWarning() {
        const loadingModal = document.getElementById('loadingModal');
        const loadingText = loadingModal?.querySelector('.loading-text');

        if (loadingModal && loadingText) {
            loadingModal.classList.add('timeout');
            loadingText.textContent = '网络较慢，请耐心等待...';
        }
    }

    // 初始化分页控件
    initPagination() {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;

        // 页码输入框处理
        const currentPageInput = pagination.querySelector('.current-page');
        currentPageInput.addEventListener('change', () => {
            const page = parseInt(currentPageInput.value);
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
                this.loadFaultList();
            } else {
                currentPageInput.value = this.currentPage;
            }
        });

        // 每页条数选择
        const pageSizeSelect = pagination.querySelector('.page-size');
        pageSizeSelect.value = this.pageSize;
        pageSizeSelect.addEventListener('change', () => {
            this.pageSize = parseInt(pageSizeSelect.value);
            this.currentPage = 1;
            this.loadFaultList();
        });

        // 分页按钮处理
        pagination.querySelector('.btn-first-page').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage = 1;
                this.loadFaultList();
            }
        });

        pagination.querySelector('.btn-prev-page').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadFaultList();
            }
        });

        pagination.querySelector('.btn-next-page').addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadFaultList();
            }
        });

        pagination.querySelector('.btn-last-page').addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage = this.totalPages;
                this.loadFaultList();
            }
        });
    }

    // 更新分页信息
    updatePagination(paginationData) {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;

        this.totalPages = paginationData.total_pages;
        this.totalRecords = paginationData.total;

        pagination.querySelector('.total-count').textContent = this.totalRecords;
        pagination.querySelector('.total-pages').textContent = this.totalPages;
        pagination.querySelector('.current-page').value = this.currentPage;

        // 更新按钮状态
        pagination.querySelector('.btn-first-page').disabled = this.currentPage === 1;
        pagination.querySelector('.btn-prev-page').disabled = this.currentPage === 1;
        pagination.querySelector('.btn-next-page').disabled = this.currentPage === this.totalPages;
        pagination.querySelector('.btn-last-page').disabled = this.currentPage === this.totalPages;
    }

    // 加载所有选项
    loadAllOptions() {
        // 首先加载科室
        this.loadOptions('section');
        
        // 加载其他选项时不带任何筛选条件
        this.loadOptions('project');
        this.loadOptions('line');
        this.loadOptions('unit');
    }

    // 加载指定类型的选项
    loadOptions(type, params = {}, customElementId = null) {
        const elementId = customElementId || `search-${type}`;
        const selectElement = document.getElementById(elementId);
        if (!selectElement) return;

        const queryParams = new URLSearchParams({ type, ...params });
        
        return fetch(`php/get_options.php?${queryParams}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 判断是搜索表单还是注册表单
                    const defaultOption = elementId.startsWith('search') ? 
                        '<option value="">ALL</option>' : 
                        '<option value="">请选择</option>';
                    
                    selectElement.innerHTML = defaultOption;
                    
                    data.data.forEach(value => {
                        const option = document.createElement('option');
                        option.value = value;
                        option.textContent = value;
                        selectElement.appendChild(option);
                    });

                    // 如果是科室选择器，设置默认值
                    if (type === 'section' && this.userInfo?.section) {
                        if (elementId === 'search-section') {
                            // 查询表单中设置默认值但允许更改
                            selectElement.value = this.userInfo.section;
                            // 触发change事件以更新其他关联选项
                            selectElement.dispatchEvent(new Event('change'));
                        } else if (elementId === 'register-section') {
                            // 注册表单中设置默认值且不允许更改
                            selectElement.value = this.userInfo.section;
                            selectElement.disabled = true;
                            // 触发change事件以更新其他关联选项
                            selectElement.dispatchEvent(new Event('change'));
                        }
                    }

                    return data;
                } else {
                    console.error(`加载${type}数据失败:`, data.message);
                    throw new Error(data.message);
                }
            })
            .catch(error => {
                console.error(`获取${type}数据时出错:`, error);
                throw error;
            });
    }

    // 初始化选项联动（查询表单）
    initOptionChaining() {
        // 获取查询表单的选择器
        const sectionSelect = document.getElementById('search-section');
        const lineSelect = document.getElementById('search-line');
        const unitSelect = document.getElementById('search-unit');

        // 科室变化时更新其他所有选项
        sectionSelect?.addEventListener('change', () => {
            const section = sectionSelect.value;
            
            // 重置其他选择器
            lineSelect.innerHTML = '<option value="">ALL</option>';
            unitSelect.innerHTML = '<option value="">ALL</option>';

            if (section) {
                // 更新线别
                this.loadOptions('line', { section })
                    .then(() => {
                        // 更新单元
                        return this.loadOptions('unit', { section });
                    })
                    .catch(error => console.error('选项加载失败:', error));
            }
        });

        // line变化时更新unit
        lineSelect?.addEventListener('change', async () => {
            const section = sectionSelect.value;
            const line = lineSelect.value;

            // 如果line未选择，加载该科室下所有unit
            const url = line ? 
                `php/get_options.php?type=unit&section=${section}&line=${line}` :
                `php/get_options.php?type=unit&section=${section}`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                if (data.success) {
                    unitSelect.innerHTML = '<option value="">ALL</option>' + 
                        data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                }
            } catch (error) {
                console.error('加载unit选项失败:', error);
            }
        });
    }

    // 处理注册表单的联动
    initRegisterOptionChaining() {
        const sectionSelect = document.getElementById('register-section');

        // 如果有科室值，直接触发加载
        if (sectionSelect && sectionSelect.value) {
            const section = sectionSelect.value;
            this.loadOptions('line', { section: section }, 'register-line');
            this.loadOptions('unit', { section: section }, 'register-unit');
        }

        // 添加line变化时更新unit的监听
        const lineSelect = document.getElementById('register-line');
        const unitSelect = document.getElementById('register-unit');

        lineSelect?.addEventListener('change', async () => {
            const section = sectionSelect.value;
            const line = lineSelect.value;

            // 如果line未选择，加载该科室下所有unit
            const url = line ? 
                `php/get_options.php?type=unit&section=${section}&line=${line}` :
                `php/get_options.php?type=unit&section=${section}`;
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                if (data.success) {
                    unitSelect.innerHTML = '<option value="">请选择</option>' + 
                        data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                }
            } catch (error) {
                console.error('加载unit选项失败:', error);
            }
        });
    }
}

// 创建故障管理器实例
const faultManager = new FaultManager(); 