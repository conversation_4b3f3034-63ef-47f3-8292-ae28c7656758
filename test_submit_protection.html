<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防重复提交测试</title>
    <link rel="stylesheet" href="css/fault.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-form {
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn-submit {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }
        .btn-submit:hover {
            background-color: #0056b3;
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>防重复提交功能测试</h1>
        
        <div class="test-info">
            <h3>测试说明：</h3>
            <ul>
                <li>点击提交按钮后，按钮会立即禁用</li>
                <li>显示全屏加载弹框，阻止其他交互</li>
                <li>模拟网络延迟（3秒后完成）</li>
                <li>30秒后显示超时警告</li>
                <li>提交完成后恢复正常状态</li>
            </ul>
        </div>

        <form class="test-form register-form" id="testForm">
            <div class="form-group">
                <label for="test-name">测试名称：</label>
                <input type="text" id="test-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="test-description">测试描述：</label>
                <textarea id="test-description" name="description" rows="4"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn-submit">提交测试</button>
            </div>
        </form>

        <div class="test-results" id="testResults" style="display: none;">
            <h3>测试结果：</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <!-- 加载弹框 -->
    <div id="loadingModal" class="loading-modal">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">提交中...</div>
        </div>
    </div>

    <script>
        // 模拟FaultManager类的防重复提交功能
        class TestSubmitProtection {
            constructor() {
                this.initializeForm();
            }

            initializeForm() {
                const form = document.getElementById('testForm');
                if (form) {
                    form.onsubmit = async (e) => {
                        e.preventDefault();
                        await this.handleSubmit(form);
                    };
                }
            }

            async handleSubmit(form) {
                // 显示加载状态
                this.showLoadingModal();
                
                // 设置超时警告
                const timeoutId = setTimeout(() => {
                    this.showTimeoutWarning();
                }, 30000);

                try {
                    // 模拟网络请求延迟
                    await this.simulateNetworkRequest();
                    
                    // 模拟成功响应
                    this.showTestResult('成功', '表单提交成功！防重复提交机制正常工作。');
                    form.reset();
                    
                } catch (error) {
                    this.showTestResult('失败', '提交失败：' + error.message);
                } finally {
                    clearTimeout(timeoutId);
                    this.hideLoadingModal();
                }
            }

            // 模拟网络请求
            simulateNetworkRequest() {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        resolve({ success: true });
                    }, 3000); // 3秒延迟
                });
            }

            showLoadingModal() {
                const loadingModal = document.getElementById('loadingModal');
                const submitBtn = document.querySelector('.btn-submit');
                
                if (loadingModal) {
                    loadingModal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                }
                
                if (submitBtn) {
                    submitBtn.disabled = true;
                }
                
                this.disablePageInteraction();
            }

            hideLoadingModal() {
                const loadingModal = document.getElementById('loadingModal');
                const loadingText = loadingModal?.querySelector('.loading-text');
                const submitBtn = document.querySelector('.btn-submit');
                
                if (loadingModal) {
                    loadingModal.style.display = 'none';
                    loadingModal.classList.remove('timeout');
                    document.body.style.overflow = 'auto';
                }
                
                if (loadingText) {
                    loadingText.textContent = '提交中...';
                }
                
                if (submitBtn) {
                    submitBtn.disabled = false;
                }
                
                this.enablePageInteraction();
            }

            disablePageInteraction() {
                const formElements = document.querySelectorAll('input, select, textarea, button');
                formElements.forEach(element => {
                    if (!element.closest('#loadingModal')) {
                        element.dataset.originalDisabled = element.disabled;
                        element.disabled = true;
                    }
                });
                document.body.classList.add('submitting');
            }

            enablePageInteraction() {
                const formElements = document.querySelectorAll('input, select, textarea, button');
                formElements.forEach(element => {
                    if (!element.closest('#loadingModal')) {
                        const originalDisabled = element.dataset.originalDisabled;
                        element.disabled = originalDisabled === 'true';
                        delete element.dataset.originalDisabled;
                    }
                });
                document.body.classList.remove('submitting');
            }

            showTimeoutWarning() {
                const loadingModal = document.getElementById('loadingModal');
                const loadingText = loadingModal?.querySelector('.loading-text');
                
                if (loadingModal && loadingText) {
                    loadingModal.classList.add('timeout');
                    loadingText.textContent = '网络较慢，请耐心等待...';
                }
            }

            showTestResult(status, message) {
                const resultsDiv = document.getElementById('testResults');
                const contentDiv = document.getElementById('resultContent');
                
                if (resultsDiv && contentDiv) {
                    contentDiv.innerHTML = `
                        <p><strong>状态：</strong>${status}</p>
                        <p><strong>消息：</strong>${message}</p>
                        <p><strong>时间：</strong>${new Date().toLocaleString()}</p>
                    `;
                    resultsDiv.style.display = 'block';
                }
            }
        }

        // 初始化测试
        document.addEventListener('DOMContentLoaded', () => {
            new TestSubmitProtection();
        });
    </script>
</body>
</html>
