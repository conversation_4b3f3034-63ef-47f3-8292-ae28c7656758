/* 故障页面图表区域样式 */
.fault-charts {
  margin: 20px 0;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.faultchart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  flex: 1;
  min-width: 300px;
  padding: 0 10px;
  margin-bottom: 20px;
}

.faultchart-container h3 {
  text-align: center;
  margin-bottom: 10px;
}

@media (max-width: 992px) {
  .faultchart-container {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* 故障操作按钮样式 */
.fault-action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.fault-action-btn {
  width: 70px;
  height: 30px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #007bff;
  color: white;
}

.fault-action-btn:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.fault-action-btn-danger {
  background-color: #dc3545;
}

.fault-action-btn-danger:hover {
  background-color: #c82333;
}

/* 加载弹框样式 */
.loading-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  min-width: 200px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 提交按钮禁用状态 */
.btn-submit:disabled {
  background-color: #6c757d !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.btn-submit:disabled:hover {
  background-color: #6c757d !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 提交状态下的页面样式 */
body.submitting {
  pointer-events: none;
}

body.submitting #loadingModal {
  pointer-events: auto;
}

/* 超时处理样式 */
.loading-modal.timeout .loading-text {
  color: #dc3545;
}

.loading-modal.timeout .loading-spinner {
  border-top-color: #dc3545;
}